cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 1500,

        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,

        // 最大跳跃次数
        maxJumpTimes: 5,

        // 玩家在X轴的固定位置
        fixedPositionX: -200,

        // 滑行持续时间
        slideDuration: 1.0,

        // 滑行时的碰撞体高度缩放
        slideColliderScale: 0.5,

        // 默认动画名称
        defaultAnimationName: "playeridle",

        // 跳跃动画持续时间（秒）
        jumpAnimationDuration: 0.5,

        // 射线检测距离
        raycastDistance: 15,

        // // 音效
        // jumpSound: {
        //     default: null,
        //     type: cc.AudioClip
        // },

        // slideSound: {
        //     default: null,
        //     type: cc.AudioClip
        // }
    },

    onLoad() {
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;

        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);

        // 获取动画组件
        this.animationComponent = this.getComponent(cc.Animation);

        // 获取碰撞体组件 - 只获取PhysicsBoxCollider
        this.collider = this.getComponent(cc.PhysicsBoxCollider);

        if (this.collider) {
            // PhysicsBoxCollider使用size属性
            this.originalColliderSize = this.collider.size.clone();
            console.log("找到PhysicsBoxCollider，尺寸:", this.collider.size);
        } else {
            console.error("未找到PhysicsBoxCollider组件！");
            this.originalColliderSize = null;
        }

        // 强制设置刚体属性，防止动画干扰
        this.rigidBody.fixedRotation = true;
        this.rigidBody.type = cc.RigidBodyType.Dynamic; // 确保是动态刚体
        this.rigidBody.gravityScale = 1; // 确保重力缩放为1
        this.rigidBody.allowSleep = false; // 禁止休眠，防止物理计算停止

        // 记录初始位置（用于重置）
        this.initialPosition = cc.v2(this.node.x, this.node.y);

        // 射线检测相关
        this.groundCheckDelay = 0;
        this.lastGroundContactTime = 0;

        // 设置动画事件监听
        this.setupAnimationEvents();

        // 绑定碰撞事件
        this.node.on('onBeginContact', this.onBeginContact, this);
        this.node.on('onEndContact', this.onEndContact, this);
    },

    start() {
        // 固定X轴位置
        this.node.x = this.fixedPositionX;

        // 播放默认动画
        this.playDefaultAnimation();
    },

    update(dt) {
        // 保持X轴位置不变
        this.node.x = this.fixedPositionX;

        // 强制确保刚体属性不被动画干扰
        if (this.rigidBody) {
            this.rigidBody.gravityScale = 1;
            this.rigidBody.allowSleep = false;
            
            // 如果刚体意外变成静态或运动学类型，强制改回动态
            if (this.rigidBody.type !== cc.RigidBodyType.Dynamic) {
                this.rigidBody.type = cc.RigidBodyType.Dynamic;
                console.log("修正刚体类型为Dynamic");
            }
        }

        // 使用射线检测辅助地面检测
        this.raycastGroundCheck();

        // 处理地面检测延迟
        if (this.groundCheckDelay > 0) {
            this.groundCheckDelay -= dt;
            if (this.groundCheckDelay <= 0) {
                this.checkGroundWithRaycast();
            }
        }

        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.initialPosition.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
    },

    // 射线检测地面 - 简化版本，只针对PhysicsBoxCollider
    raycastGroundCheck() {
        if (!this.collider) {
            return;
        }

        // PhysicsBoxCollider直接使用size.height
        let colliderHeight = this.collider.size.height;

        // 从角色底部中心发射射线向下
        let startPoint = cc.v2(
            this.node.x,
            this.node.y - colliderHeight / 2
        );
        let endPoint = cc.v2(
            this.node.x,
            this.node.y - colliderHeight / 2 - this.raycastDistance
        );

        // 执行射线检测
        let results = cc.director.getPhysicsManager().rayCast(startPoint, endPoint, cc.RayCastType.Closest);

        if (results.length > 0) {
            let result = results[0];
            // 检查碰撞的是否是地面
            if (result.collider.node.group === 'Ground') {
                // 如果角色向下运动或静止，且射线距离很近，认为在地面上
                if (this.rigidBody.linearVelocity.y <= 50 && result.fraction < 0.3) {
                    if (!this.isOnGround) {
                        this.isOnGround = true;
                        this.jumpCount = 0;
                        console.log("射线检测：角色落地");
                    }
                }
            }
        } else {
            // 没有检测到地面，但给一个小的缓冲时间
            if (this.isOnGround && this.rigidBody.linearVelocity.y > 10) {
                this.groundCheckDelay = 0.1; // 100ms缓冲
            }
        }
    },

    // 延迟检测地面（用于离开地面时的缓冲）
    checkGroundWithRaycast() {
        if (!this.collider) {
            return;
        }

        // PhysicsBoxCollider直接使用size.height
        let colliderHeight = this.collider.size.height;

        let startPoint = cc.v2(
            this.node.x,
            this.node.y - colliderHeight / 2
        );
        let endPoint = cc.v2(
            this.node.x,
            this.node.y - colliderHeight / 2 - this.raycastDistance
        );

        let results = cc.director.getPhysicsManager().rayCast(startPoint, endPoint, cc.RayCastType.Closest);

        if (results.length === 0 || results[0].collider.node.group !== 'Ground') {
            this.isOnGround = false;
            console.log("射线检测：角色离开地面");
        }
    },

    setupAnimationEvents() {
        // 使用定时器方式来处理动画切换，更可靠
        console.log("动画事件设置完成");
    },

    playDefaultAnimation() {
        if (this.animationComponent && this.defaultAnimationName) {
            // 检查是否有默认动画
            let animState = this.animationComponent.getAnimationState(this.defaultAnimationName);
            if (animState) {
                this.animationComponent.play(this.defaultAnimationName);
                console.log("播放默认动画:", this.defaultAnimationName);
                
                // 播放动画后立即确保物理属性正确
                this.scheduleOnce(() => {
                    this.ensurePhysicsProperties();
                }, 0.1);
            } else {
                console.warn("没有找到默认动画:", this.defaultAnimationName);
            }
        }
    },

    // 确保物理属性不被动画干扰
    ensurePhysicsProperties() {
        if (this.rigidBody) {
            this.rigidBody.type = cc.RigidBodyType.Dynamic;
            this.rigidBody.gravityScale = 1;
            this.rigidBody.allowSleep = false;
            this.rigidBody.fixedRotation = true;
            console.log("重新确保物理属性正确");
        }
    },

    jump() {
        if (this.isGameOver || this.isSliding) {
            return;
        }

        // 跳跃条件：在地面上或者还有剩余跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);

            // 增加跳跃计数
            this.jumpCount++;

            // 标记不在地面上
            this.isOnGround = false;

            // 播放跳跃动画
            if (this.animationComponent) {
                this.animationComponent.play('playerjump');

                // 设置定时器切换回默认动画
                this.scheduleOnce(() => {
                    this.playDefaultAnimation();
                }, this.jumpAnimationDuration);

                console.log("跳跃动画播放，", this.jumpAnimationDuration, "秒后切换回默认动画");
            }

            console.log("玩家执行跳跃，当前跳跃次数:", this.jumpCount);

            // 跳跃成功，可以在这里播放音效
            // if (this.jumpSound) {
            //     cc.audioEngine.playEffect(this.jumpSound, false);
            // }
        }
    },

    slide() {
        if (this.isGameOver || this.isSliding || !this.isOnGround) {
            return;
        }

        console.log("玩家执行滑行");

        // 设置滑行状态
        this.isSliding = true;

        // 播放滑行动画
        if (this.animationComponent) {
            this.animationComponent.play('playerslide');
        }

        // 缩小碰撞体（模拟蹲下）
        if (this.collider && this.originalColliderSize) {
            this.collider.size = cc.size(
                this.originalColliderSize.width,
                this.originalColliderSize.height * this.slideColliderScale
            );
            this.collider.apply();
        }

        // 设置滑行结束定时器（使用滑行持续时间）
        this.scheduleOnce(() => {
            this.endSlide();
        }, this.slideDuration);

        // 播放滑行音效
        // if (this.slideSound) {
        //     cc.audioEngine.playEffect(this.slideSound, false);
        // }
    },

    endSlide() {
        if (!this.isSliding) {
            return;
        }

        console.log("玩家滑行结束");

        // 重置滑行状态
        this.isSliding = false;

        // 恢复碰撞体大小
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 播放默认动画
        this.playDefaultAnimation();
    },

    onBeginContact(contact, selfCollider, otherCollider) {
        let isPlatform = otherCollider.node.group === 'Ground';

        if (isPlatform) {
            // 记录接触时间
            this.lastGroundContactTime = Date.now();
            
            // 简化地面检测：只要碰到地面且玩家向下运动或静止，就认为落地
            if (this.rigidBody.linearVelocity.y <= 100) {
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
                console.log("碰撞检测：角色落地");
            }
        }
    },

    onEndContact(contact, selfCollider, otherCollider) {
        // 如果离开地面（通过分组检测）
        let isPlatform = otherCollider.node.group === 'Ground';

        if (isPlatform) {
            // 不立即设置离开地面，而是设置一个延迟检测
            this.groundCheckDelay = 0.05; // 50ms延迟
            console.log("碰撞检测：准备检查是否离开地面");
        }
    },

    gameOver() {
        this.isGameOver = true;
        console.log("游戏结束");

        // 播放失败音效
        // if (this.fallSound) {
        //     cc.audioEngine.playEffect(this.fallSound, false);
        // }

        // cc.director.pause(); // 游戏暂停
    },

    reset() {
        // 重置位置
        this.node.x = this.initialPosition.x;
        this.node.y = this.initialPosition.y;

        // 重置状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;
        this.groundCheckDelay = 0;
        this.lastGroundContactTime = 0;

        // 重置速度
        if (this.rigidBody) {
            this.rigidBody.linearVelocity = cc.v2(0, 0);
        }

        // 重置碰撞体
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    },

    onDestroy() {
        // 移除碰撞事件监听
        this.node.off('onBeginContact', this.onBeginContact, this);
        this.node.off('onEndContact', this.onEndContact, this);

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    }
});